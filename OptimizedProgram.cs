using DotNetEnv; // Ajout de l'import pour DotNetEnv

namespace Amazon2FacebookPoster
{
        /// <summary>
        /// Run hybrid extraction (recommended)
        /// </summary>
        private static async Task<bool> RunHybridExtractionAsync()
        {
            Console.Clear();
            Console.WriteLine("🪛 Running hybrid strategy...");
            Console.WriteLine("• Direct CSS scraping...");

            int stepPause = 2000; // Delay between stages
            Console.WriteLine("• Initializing AI model validation...");
            await Task.Delay(stepPause);

            Console.WriteLine("• Extracting product data...");
            await Task.Delay(stepPause);

            Console.WriteLine("• Analyzing product categories...");
            await Task.Delay(stepPause);

            Console.WriteLine("• Generating affiliate link...");
            await Task.Delay(stepPause);

            Console.WriteLine("• Converting to JSON...");
            await Task.Delay(stepPause);

            Console.Write("Press any key to continue...");
            Console.ReadKey();
            return true;
        }

        /// <summary>
        /// Clean up and finalize menu operations
        /// </summary>
        private static Task CleanupPostExecution()
        {
            Console.WriteLine("Removing temp files...");
            return Task.CompletedTask; // Placeholder
        }

        /// <summary>
        /// Handle invalid menu choices
        /// </summary>
        private static Task HandleInvalidChoiceAsync()
        {
            Console.WriteLine("❌ Invalid option. Returning to main menu.");
            return Task.Delay(1500);
        }

        // Entry point
        static async Task Main(string[] args)
        {
            // Validate config file presence
            var configValidator = new ConfigurationValidator();
            bool validConfig = await configValidator.Validate();

            if (!validConfig)
            {
                Console.WriteLine("❌ Configuration failed. Exiting...");
                return;
            }

            // Main menu loop
            while (true)
            {
                var userChoice = await DisplayMainMenu();
                await ProcessAmazonProducts(userChoice);

                // Clean up temp data
                await CleanupPostExecution();
            }
        }

        // Helper methods (replace with actual implementations)

        /// <summary>
        /// Display main menu and gather user input
        /// </summary>
        private static async Task<string> DisplayMainMenu()
        {
            Console.Clear();
            Console.WriteLine("Amazon2Facebook Poster V5 - Menu");
            Console.WriteLine();
            Console.WriteLine("[1] Start Extraction Process");
            // Add more menu options here
            Console.WriteLine("[0] Exit");
            Console.WriteLine();

            Console.Write("Choose an option: ");
            return Console.ReadLine() ?? string.Empty;
        }

        /// <summary>
        /// Placeholder extraction processor
        /// </summary>
        

        /// <summary>
        /// Quick test with minimal configuration
        /// </summary>
        private static async Task<bool> RunQuickTestAsync()
        {
            Console.Clear();
            Console.WriteLine("🧪 QUICK TEST MODE");
            Console.WriteLine("=" + new string('=', 20));
            Console.WriteLine();

            try
            {
                await Task.Delay(1000);
                var result = AmazonScrapingConfiguration.Validate(); // Simulating quick test process
                Console.WriteLine("📝 Quick test completed successfully: " + result);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Quick test failed: {ex.Message}");
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to return to menu...");
            Console.ReadKey();
            return await Task.FromResult(true);
        }

        /// <summary>
        /// Preview products without generating posts
        /// </summary>
        private static async Task<bool> RunPreviewAsync()
        {
            Console.Clear();
            Console.WriteLine("👀 PREVIEW MODE");
            Console.WriteLine("=" + new string('=', 15));
            Console.WriteLine();

            try
            {
                var config = AmazonScrapingConfiguration.CreatePreviewConfiguration();
                var products = await AmazonProductPreview.GenerateAsync(config);
                foreach (var product in products)
                {
                    Console.WriteLine($"📦 {product.Title} (${product.Price})");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Failed to preview products: {ex.Message}");
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to return to menu...");
            
            }

            Console.WriteLine("👋 Thank you for using Amazon2FacebookPoster!");
        }

        // La méthode InitializeConfigurationAsync n'est plus nécessaire car la configuration
        // est gérée directement par AmazonScrapingConfiguration et les variables d'environnement.
        // Si des clés sont manquantes, la validation de AmazonScrapingConfiguration les signalera.

        /// <summary>
        /// Display the main menu
        /// </summary>
        private static void DisplayMainMenu()
        {
            Console.Clear();
            Console.WriteLine("🛒 Amazon2FacebookPoster - Main Menu");
            Console.WriteLine("=" + new string('=', 40));
            Console.WriteLine();
            Console.WriteLine("1. Process Products (Extract & Generate)");
            Console.WriteLine("2. Configuration Tools (Manage & Test)");
            Console.WriteLine();
            Console.WriteLine("3. Quit Application");
            Console.WriteLine();
            Console.Write("Choose an option (1-3): ");
        }

        /// <summary>
        /// Handle menu choice
        /// </summary>
        private static async Task<bool> HandleMenuChoiceAsync(string? choice)
        {
            return choice switch
            {
                "1" => await HandleProcessOption(),
                "2" => await HandleConfigurationTool(),
                "3" => true, // Exit application
                _ => await HandleInvalidChoiceAsync()
            };
        }

        /// <summary>
        /// Process products extraction flow
        /// </summary>
        private static async Task<bool> HandleProcessOption()
        {
            await DisplaySubMenu("Processing Options",
                ("[1] Smart Extraction", RunHybridExtractionAsync),
                ("[2] Direct CSS Scraping", RunDirectExtractionAsync),
                ("[3] AI-Only Extraction", RunAIExtractionAsync),
                ("[4] Configurable Extraction", RunConfigurableExtractionAsync));

            return false; // Return to main menu
        }

        /// <summary>
        /// Configuration tool entry point
        /// </summary>
        private static async Task<bool> HandleConfigurationTool()
        {
            await DisplaySubMenu("Configuration Tools",
                ("[5] Quick Test", RunQuickTestAsync),
                ("[6] Preview Products", RunPreviewAsync),
                ("[7] Process from JSON", RunFromJsonAsync),
                ("[8] Config Management", RunConfigurationManagementAsync));

            return false; // Return to main menu
        }


        /// <summary>
        /// Displays a submenu with given options and handles execution
        /// </summary>
        private static async Task DisplaySubMenu(string title, params (string optionText, Func<Task<bool>> action)[] options)
        {
            Console.Clear();
            Console.WriteLine(title);
            Console.WriteLine("-" + new string('-', title.Length));
            Console.WriteLine();

            foreach (var (optionText, _) in options)
            {
                Console.WriteLine(optionText);
            }

            Console.WriteLine();
            Console.WriteLine("[0] Back to Main Menu");
            Console.Write("Choose an option: ");

            var submenuChoice = Console.ReadLine();
            bool result = false;

            switch (submenuChoice)
            {
                case "0":
                    break; // Back to Main Menu
                case "1":
                    result = await options[0].action();
                    break;
                case "2":
                    result = await options[1].action();
                    break;
                case "3":
                    result = await options[2].action();
                    break;
                case "4":
                    result = await options[3].action();
                    break; 
                case "5":
                    result = await options[0].action(); // Default implementation assumes action 0
                    break;
                case "6":
                    result = await options[1].action(); // Default implementation assumes action 1
                    break;
                case "7":
                    result = await options[2].action(); // Default implementation assumes action 2
                    break;
                case "8":
                    result = await options[3].action();
                    break;
                default:
                    await HandleInvalidChoiceAsync();
                    break;
            }

            if (!result)
            {
                await HandleInvalidChoiceAsync();
            }
        }

        /// 



        /// <summary>
        /// Run hybrid extraction (recommended)
        /// </summary>
        private static async Task<bool> RunHybridExtractionAsync()
        {
            Console.Clear();
            Console.WriteLine("🎯 HYBRID EXTRACTION (RECOMMENDED)");
            Console.WriteLine("=" + new string('=', 40));
            Console.WriteLine("• Direct CSS scraping with AI fallback");
            Console.WriteLine("• Best balance of speed and accuracy");
            Console.WriteLine("• Handles Amazon layout changes automatically");
            Console.WriteLine();

            var config = await GetUserConfigurationAsync(ExtractionStrategy.Hybrid);
            if (config == null) return false;

            var result = await ExecuteProcessingAsync(config);
            return result;
        }

        /// <summary>
        /// Run direct CSS extraction
        /// </summary>
        private static async Task<bool> RunDirectExtractionAsync()
        {
            Console.Clear();
            Console.WriteLine("⚡ DIRECT CSS SCRAPING");
            Console.WriteLine("=" + new string('=', 30));
            Console.WriteLine("• Fast CSS selector-based scraping");
            Console.WriteLine("• Most efficient for current Amazon layout");
            Console.WriteLine("• May need updates if Amazon changes structure");
            Console.WriteLine();

            var config = await GetUserConfigurationAsync(ExtractionStrategy.Direct);
            if (config == null) return false;

            var result = await ExecuteProcessingAsync(config);
            return result;
        }

        /// <summary>
        /// Run AI-only extraction
        /// </summary>
        private static async Task<bool> RunAIExtractionAsync()
        {
            Console.Clear();
            Console.WriteLine("🤖 AI-ONLY EXTRACTION");
            Console.WriteLine("=" + new string('=', 25));
            Console.WriteLine("• Comprehensive AI-based analysis");
            Console.WriteLine("• Adapts to any Amazon layout changes");
            Console.WriteLine("• Slower but most thorough");
            Console.WriteLine();

            var config = await GetUserConfigurationAsync(ExtractionStrategy.AI);
            if (config == null) return false;

            var result = await ExecuteProcessingAsync(config);
            return result;
        }

        /// <summary>
        /// Run configurable extraction
        /// </summary>
        private static async Task<bool> RunConfigurableExtractionAsync()
        {
            Console.Clear();
            Console.WriteLine("🔧 CONFIGURABLE EXTRACTION");
            Console.WriteLine("-" + new string('-', 30));
            Console.WriteLine();
            Console.WriteLine("• Uses external configuration rules");
            Console.WriteLine("• Easily customizable without code changes");
            Console.WriteLine("• Hot-reload configuration support");
            Console.WriteLine("• Perfect for non-technical users");
            Console.WriteLine();

            var config = await GetUserConfigurationAsync(ExtractionStrategy.Configurable);
            if (config == null) return false;

            return await ExecuteProcessingAsync(config);
        }

        /// <summary>
        /// Run configuration management tools
        /// </summary>
        private static async Task<bool> RunConfigurationManagementAsync()
        {
            Console.Clear();
            Console.WriteLine("⚙️ CONFIGURATION MANAGEMENT");
            Console.WriteLine("=" + new string('-', 30));
            Console.WriteLine("• Manage extraction rules");
            Console.WriteLine("• Test selectors on live pages");
            Console.WriteLine("• Validate configurations");
            Console.WriteLine("• Create and edit rules");
            Console.WriteLine();

            try
            {
                var tools = new ConfigurationManagementTools();
                var result = await tools.RunConfigurationEditorAsync();

                // Assume RunConfigurationEditorAsync returns a boolean result
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Configuration management failed: {ex.Message}");
                Console.WriteLine("Press any key to return to menu...");
                Console.ReadKey();
                return false;
            }
        }

        /// <summary>
        /// Get user configuration for processing
        /// </summary>
        private static async Task<AmazonScrapingConfiguration?> GetUserConfigurationAsync(ExtractionStrategy strategy)
        {
            try
            {
                Console.Write("Number of pages to process (1-10, recommended: 3-5): ");
                var pagesInput = Console.ReadLine();
                var maxPages = int.TryParse(pagesInput, out var pages) ? Math.Clamp(pages, 1, 10) : 3;

                Console.Write("Headless mode? (y/n, recommended: n to see progress): ");
                var headlessInput = Console.ReadLine()?.ToLower();
                var headless = headlessInput == "y" || headlessInput == "yes";

                Console.Write("Generate Facebook posts? (y/n, recommended: y): ");
                var postsInput = Console.ReadLine()?.ToLower();
                var generatePosts = postsInput != "n" && postsInput != "no";

                var config = AmazonScrapingConfiguration.CreateCustomConfiguration(
                    strategy,
                    null, // GeminiApiKey sera lu depuis les variables d'environnement ou la valeur par défaut
                    null, // AmazonAssociateTag sera lu depuis les variables d'environnement ou la valeur par défaut
                    maxPages,
                    headless,
                    generatePosts);

                var validation = config.Validate();
                if (validation != System.ComponentModel.DataAnnotations.ValidationResult.Success)
                {
                    Console.WriteLine($"❌ Configuration error: {validation.ErrorMessage}");
                    return null;
                }

                return config;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Configuration error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Execute the processing with progress reporting
        /// </summary>
        private static async Task<bool> ExecuteProcessingAsync(AmazonScrapingConfiguration config)
        {
            Console.WriteLine();
            Console.WriteLine("🚀 Starting processing...");
            Console.WriteLine($"📊 Strategy: {config.ExtractionStrategy}");
            Console.WriteLine($"📄 Pages: {config.MaxPages}");
            Console.WriteLine($"📁 Output: {config.OutputDirectory}");
            Console.WriteLine();

            using var processor = new UnifiedAmazonProcessor(config);
            var result = await processor.ProcessAsync();

            Console.WriteLine();
            Console.WriteLine(result.GetSummaryReport());

            if (result.Success)
            {
                Console.WriteLine($"📁 Check output directory: {result.OutputDirectory}");
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to return to menu...");
            Console.ReadKey();
            return false;
        }

        /// <summary>
        /// Quick test with minimal configuration
        /// </summary>
        private static async Task<bool> RunQuickTestAsync()
        {
            Console.Clear();
            Console.WriteLine("🧪 QUICK TEST MODE");
            Console.WriteLine("=" + new string('=', 20));
            Console.WriteLine("• 1 page, limited products");
            Console.WriteLine("• Fast validation of setup");
            Console.WriteLine();

            using var processor = UnifiedAmazonProcessor.CreateForTesting();
            var result = await processor.ProcessSinglePageAsync();

            Console.WriteLine();
            Console.WriteLine(result.GetSummaryReport());
            Console.WriteLine();
            Console.WriteLine("Press any key to return to menu...");
            Console.ReadKey();
            return false;
        }

        /// <summary>
        /// Preview products without generating posts
        /// </summary>
        private static async Task<bool> RunPreviewAsync()
        {
            Console.Clear();
            Console.WriteLine("👀 PREVIEW MODE");
            Console.WriteLine("=" + new string('=', 15));

            // Les clés API sont maintenant gérées par la configuration elle-même
            var config = AmazonScrapingConfiguration.CreateTestConfiguration();
            config.GeneratePosts = false;

            using var processor = new UnifiedAmazonProcessor(config);
            var products = await processor.PreviewProductsAsync();

            Console.WriteLine($"📊 Found {products.Count} products");
            Console.WriteLine();
            Console.WriteLine("Press any key to return to menu...");
            Console.ReadKey();
            return false;
        }

        /// <summary>
        /// Process from existing JSON file
        /// </summary>
        private static async Task<bool> RunFromJsonAsync()
        {
            Console.Clear();
            Console.WriteLine("📂 PROCESS FROM JSON");
            Console.WriteLine("=" + new string('=', 20));

            Console.Write("Enter JSON file path: ");
            var jsonPath = Console.ReadLine()?.Trim();

            if (string.IsNullOrEmpty(jsonPath) || !File.Exists(jsonPath))
            {
                Console.WriteLine("❌ File not found!");
                Console.WriteLine("Press any key to return to menu...");
                Console.ReadKey();
                return false;
            }

            var config = AmazonScrapingConfiguration.CreateCustomConfiguration(
                ExtractionStrategy.Hybrid, // Default strategy for JSON processing
                null, // GeminiApiKey sera lu depuis les variables d'environnement ou la valeur par défaut
                null, // AmazonAssociateTag sera lu depuis les variables d'environnement ou la valeur par défaut
                1, // Single page since we're processing from JSON
                false, // Not headless for JSON processing
                true); // Generate posts

            // Override output directory for JSON processing
            config.OutputDirectory = $"json_output_{DateTime.Now:yyyyMMdd_HHmmss}";

            using var processor = new UnifiedAmazonProcessor(config);
            var result = await processor.ProcessFromJsonAsync(jsonPath);

            Console.WriteLine();
            Console.WriteLine(result.GetSummaryReport());
            Console.WriteLine();
            Console.WriteLine("Press any key to return to menu...");
            Console.ReadKey();
            var success = result?.Success ?? false;
            Console.WriteLine($"\n{ (success ? "✅" : "❌") } Processing completed{(success ? " successfully!" : " with issues.")}");
            Console.WriteLine("Press any key to return to menu...");
            Console.ReadKey();
            return success;
        }

        /// <summary>
        /// Quick test with minimal configuration
        /// </summary>
        private static async Task<bool> RunQuickTestAsync()
        {
            Console.Clear();
            Console.WriteLine("🧪 QUICK TEST MODE");
            Console.WriteLine("=" + new string('=', 20));
            Console.WriteLine("• 1 page, limited products");
            Console.WriteLine("• Fast validation of setup");
            Console.WriteLine();

            using var processor = UnifiedAmazonProcessor.CreateForTesting();
            var result = await processor.ProcessSinglePageAsync();

            Console.WriteLine();
            Console.WriteLine(result.GetSummaryReport());

            var success = result?.Success ?? false;
            Console.WriteLine($"\n{ (success ? "✅" : "❌") } Testing completed{(success ? " successfully!" : " with issues.")}");
            Console.WriteLine("Press any key to return to menu...");
            Console.ReadKey();
            return success;
        }

        /// <summary>
        /// Preview products without generating posts
        /// </summary>
        private static async Task<bool> RunPreviewAsync()
        {
            Console.Clear();
            Console.WriteLine("👀 PREVIEW MODE");
            Console.WriteLine("=" + new string('=', 15));

            // Les clés API sont maintenant gérées par la configuration elle-même
            var config = AmazonScrapingConfiguration.CreateTestConfiguration();
            config.GeneratePosts = false;

            using var processor = new UnifiedAmazonProcessor(config);
            var products = await processor.PreviewProductsAsync();

            Console.WriteLine($"📊 Found {(products?.Count ?? 0)} products");

            var success = products?.Count > 0;
            Console.WriteLine($"\n{ (success ? "✅" : "❌") } Products previewed{(success ? "!" : " failed.")}");
            Console.WriteLine("Press any key to return to menu...");
            Console.ReadKey();
            return success;
        }

        /// <summary>
        /// Process from existing JSON file
        /// </summary>
        private static async Task<bool> RunFromJsonAsync()
        {
            Console.Clear();
            Console.WriteLine("📂 PROCESS FROM JSON");
            Console.WriteLine("=" + new string('=', 20));

            Console.Write("Enter JSON file path (full path or 'example.json'): ");
            var inputPath = Console.ReadLine()?.Trim() ?? "";

            // Allow relative/root paths
            string jsonPath = inputPath;
            if (!Path.IsPathRooted(jsonPath))
            {
                jsonPath = Path.Combine(Environment.CurrentDirectory, inputPath);
            }

            if (!File.Exists(jsonPath))
            {
                Console.WriteLine("❌ File not found!");
                Console.WriteLine("Press any key to return to menu...");
                Console.ReadKey();
                return false;
            }

            var config = AmazonScrapingConfiguration.CreateCustomConfiguration(
                ExtractionStrategy.Hybrid, // Default strategy for JSON processing
                null,                      // GeminiApiKey sera lu depuis les variables d'environnement ou valeur par défaut
                null,                      // AmazonAssociateTag sera lu depuis les variables d'environnement ou valeur par défaut
                1,                         // Pages unique _(as we process from JSON)
                false,                     // Not headless for JSON operations
                true);                     // Generate posts

            // Override output directory
            config.OutputDirectory = $"json_output_{DateTime.Now:yyyyMMdd_HHmmss}";

            using var processor = new UnifiedAmazonProcessor(config);
            var result = await processor.ProcessFromJsonAsync(jsonPath);

            Console.WriteLine();
            Console.WriteLine(result.GetSummaryReport());

            var success = result?.Success ?? false;
            Console.WriteLine($"\n{ (success ? "✅" : "❌") } JSON processing completed{(success ? " successfully!" : " with issues.")}");
            Console.WriteLine("Press any key to return to menu...");
            Console.ReadKey();
            return success;
        }

        /// <summary>
        /// Run configuration management tools
        /// </summary>
        private static async Task<bool> RunConfigurationManagementAsync()
        {
            Console.Clear();
            Console.WriteLine("⚙️ CONFIGURATION MANAGEMENT");
            Console.WriteLine("=" + new string('-', 30));
            Console.WriteLine("• Manage extraction rules");
            Console.WriteLine("• Test selectors on live pages");
            Console.WriteLine("• Validate configurations");
            Console.WriteLine("• Create and edit rules");
            Console.WriteLine();

            try
            {
                var toolsConfig = AmazonScrapingConfiguration.CreateConfigurationEditorConfig();
                var tools = new ConfigurationManagementTools(toolsConfig);
                var configResult = await tools.RunConfigurationEditorAsync();

                // Validate if the config editor was successful (assuming it returns results)
                if (configResult)
                {
                    Console.WriteLine("✅ Configuration changes applied successfully.");
                    Console.WriteLine("Press any key to return to menu...");
                    Console.ReadKey();
                    return true;
                }
                else
                {
                    Console.WriteLine("🔄 No configuration changes detected.");
                    Console.WriteLine("Press any key to return to menu...");
                    Console.ReadKey();
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Configuration management failed: {ex.Message}");
                Console.WriteLine("Press any key to return to menu...");
                Console.ReadKey();
                return false;
            }
        }

        /// <summary>
        /// Handle invalid menu choice
        /// </summary>
        private static Task HandleInvalidChoiceAsync()
        {
            Console.WriteLine("❌ Invalid option. Please try again.");
            Console.WriteLine("Returning to main menu...");
            Task.Delay(1000).Wait(); // Adjust delay for user feedback
            return Task.FromResult(false);
        }

        /// <summary>
        /// Main menu loop display and handling
        /// </summary>
        private static async Task<bool> HandleMenuChoiceAsync(string? choice)
        {
            return choice switch
            {
                "1" => await HandleProcessOption(),
                "2" => await HandleConfigurationTool(),
                "3" => true, // Exit application
                _ => await HandleInvalidChoiceAsync() // Invalid choice returns false to loop again
            };
        }


        /// <summary>
        /// Simplified async menu handler with sync fallback
        /// </summary>
        private static void AsyncWrapper<T>(Task<T> task, Action<T> callback)
        {
            var result = task.ConfigureAwait(false).GetAwaiter().GetResult();
            callback(result);
        }

        /// <summary>
        /// Helper: Get numeric input with retries
        /// </summary>
        private static int GetIntegerInput(string prompt, int min = 1, int max = 99)
        {
            while (true)
            {
                Console.Write(prompt);
                var input = Console.ReadLine();
                if (int.TryParse(input, out var number) && number >= min && number <= max)
                {
                    return number;
                }
                Console.WriteLine($"❌ Invalid input. Please enter a number between {min} and {max}.");
            }
        }

        /// <summary>
        /// Helper: Confirm yes/no choice with validation
        /// </summary>
        private static bool ConfirmChoice(string prompt)
        {
            while (true)
            {
                Console.Write($"{prompt} (y/n): ");
                var input = Console.ReadLine()?.Trim().ToLower();
                if (input == "y" || input == "yes")
                {
                    return true;
                }
                if (input == "n" || input == "no")
                {
                    return false;
                }
                Console.WriteLine("❌ Invalid choice. Press 'y' for yes or 'n' for no.");
            }
        }

    }
}
