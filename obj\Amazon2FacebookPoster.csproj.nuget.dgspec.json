{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\Amazon2FacebookPoster_V4_V1\\Amazon2FacebookPoster.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\Amazon2FacebookPoster_V4_V1\\Amazon2FacebookPoster.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\Amazon2FacebookPoster_V4_V1\\Amazon2FacebookPoster.csproj", "projectName": "Amazon2FacebookPoster", "projectPath": "C:\\Users\\<USER>\\source\\repos\\Amazon2FacebookPoster_V4_V1\\Amazon2FacebookPoster.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\Amazon2FacebookPoster_V4_V1\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AngleSharp.Css": {"target": "Package", "version": "[0.17.0, )"}, "DotNetEnv": {"target": "Package", "version": "[3.1.1, )"}, "Google_GenerativeAI": {"target": "Package", "version": "[2.7.0, )"}, "Mscc.GenerativeAI": {"target": "Package", "version": "[2.6.3, )"}, "PuppeteerSharp": {"target": "Package", "version": "[20.1.3, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}