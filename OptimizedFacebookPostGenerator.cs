using GenerativeAI;
using System.Collections.Concurrent;

namespace Amazon2FacebookPoster
{
    /// <summary>
    /// Optimized Facebook post generator with caching, retry logic, and unified approach
    /// </summary>
    public class OptimizedFacebookPostGenerator : IDisposable
    {
        private readonly GenerativeModel _model;
        private readonly ConcurrentDictionary<string, string> _postCache = new();
        private readonly SemaphoreSlim _rateLimitSemaphore = new(5, 5); // Max 5 concurrent requests
        private readonly TimeSpan _cacheExpiry = TimeSpan.FromHours(24);
        private readonly Dictionary<string, DateTime> _cacheTimestamps = new();
        private bool _disposed = false;

        // Rate limiting
        private DateTime _lastApiCall = DateTime.MinValue;
        private readonly TimeSpan _minApiInterval = TimeSpan.FromSeconds(1);

        public OptimizedFacebookPostGenerator(string apiKey)
        {
            if (string.IsNullOrWhiteSpace(apiKey))
                throw new ArgumentException("API key cannot be null or empty", nameof(apiKey));

            _model = new GenerativeModel(apiKey, "gemini-2.0-flash-exp");
        }

        /// <summary>
        /// Generate Facebook post from ProductInfo with caching and optimization
        /// </summary>
        public async Task<string> GeneratePostAsync(ProductInfo product)
        {
            if (product == null || !product.IsValid())
                throw new ArgumentException("Invalid product information", nameof(product));

            // Check cache first
            var cacheKey = GenerateCacheKey(product);
            if (TryGetFromCache(cacheKey, out var cachedPost))
            {
                Console.WriteLine($"📋 Using cached post for: {product.GetSummary()}");
                return cachedPost;
            }

            await _rateLimitSemaphore.WaitAsync();
            try
            {
                // Rate limiting
                await EnforceRateLimit();

                var prompt = CreateOptimizedPrompt(product);
                var post = await GenerateWithRetryAsync(prompt, product.ProductUrl);

                // Cache the result
                CachePost(cacheKey, post);

                return post;
            }
            finally
            {
                _rateLimitSemaphore.Release();
            }
        }

        /// <summary>
        /// Generate Facebook post from URL with basic prompt (simplified approach)
        /// </summary>
        public async Task<string> GeneratePostFromUrlAsync(string productUrl, string affiliateLink)
        {
            if (string.IsNullOrWhiteSpace(productUrl) || !Uri.TryCreate(productUrl, UriKind.Absolute, out _))
                throw new ArgumentException("Invalid product URL", nameof(productUrl));

            var cacheKey = $"url_{productUrl.GetHashCode():X}";
            if (TryGetFromCache(cacheKey, out var cachedPost))
            {
                Console.WriteLine($"📋 Using cached URL post for: {productUrl}");
                return cachedPost;
            }

            await _rateLimitSemaphore.WaitAsync();
            try
            {
                await EnforceRateLimit();

                // Use basic prompt approach instead of URL context
                var prompt = CreateUrlBasedPrompt(productUrl, affiliateLink);
                var post = await GenerateWithRetryAsync(prompt, productUrl);

                CachePost(cacheKey, post);
                return post;
            }
            finally
            {
                _rateLimitSemaphore.Release();
            }
        }

        /// <summary>
        /// Batch generate posts for multiple products with optimization
        /// </summary>
        public async Task<Dictionary<ProductInfo, string>> GenerateBatchPostsAsync(
            IEnumerable<ProductInfo> products, 
            int maxConcurrency = 3)
        {
            var validProducts = products.Where(p => p.IsValid()).ToList();
            var results = new ConcurrentDictionary<ProductInfo, string>();
            
            Console.WriteLine($"📝 Generating {validProducts.Count} posts with max {maxConcurrency} concurrent requests...");

            var semaphore = new SemaphoreSlim(maxConcurrency, maxConcurrency);
            var tasks = validProducts.Select(async product =>
            {
                await semaphore.WaitAsync();
                try
                {
                    var post = await GeneratePostAsync(product);
                    results[product] = post;
                    Console.WriteLine($"✅ Generated post for: {product.GetSummary()}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Failed to generate post for {product.Title}: {ex.Message}");
                    results[product] = CreateErrorPost(product, ex.Message);
                }
                finally
                {
                    semaphore.Release();
                }
            });

            await Task.WhenAll(tasks);
            semaphore.Dispose();

            Console.WriteLine($"🎉 Batch generation completed: {results.Count} posts generated");
            return results.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }

        /// <summary>
        /// Create optimized prompt for product information
        /// </summary>
        private string CreateOptimizedPrompt(ProductInfo product)
        {
            return $"""
                Créez un post Facebook engageant et optimisé pour ce produit Amazon :

                📦 INFORMATIONS PRODUIT :
                • Titre : {product.Title}
                • Prix : {product.Price}
                • Réduction : {product.Discount}
                • Note : {product.Rating}
                • Catégorie : {product.Category}
                • Commission : {product.CommissionRate}

                🔗 LIENS :
                • Produit : {product.ProductUrl}
                • Affiliation : {product.AffiliateLink}

                📝 CONSIGNES DE RÉDACTION :
                1. Commencez par un emoji accrocheur et un titre percutant
                2. Mettez en avant les bénéfices et la valeur ajoutée
                3. Mentionnez la réduction de manière attractive
                4. Utilisez des emojis pertinents (max 5-6)
                5. Incluez un call-to-action engageant
                6. Ajoutez 3-5 hashtags pertinents
                7. Gardez un ton naturel et authentique
                8. Maximum 280 caractères pour l'engagement optimal

                🎯 STRUCTURE SOUHAITÉE :
                [Emoji] [Titre accrocheur]
                [Description courte avec bénéfices]
                [Mention de la réduction]
                [Call-to-action]
                [Lien d'affiliation]
                [Hashtags]

                Rédigez uniquement le post, sans commentaires additionnels.
                """;
        }

        /// <summary>
        /// Create URL-based prompt for Gemini
        /// </summary>
        private string CreateUrlBasedPrompt(string productUrl, string affiliateLink)
        {
            return $"""
                Créez un post Facebook engageant pour ce produit Amazon : {productUrl}

                Lien d'affiliation à utiliser : {affiliateLink}

                Créez un post qui :
                1. Met en avant les points forts du produit
                2. Utilise un langage naturel et authentique
                3. Inclut des emojis pertinents (max 5-6)
                4. Mentionne les promotions/réductions si disponibles
                5. Se termine par un call-to-action et le lien d'affiliation
                6. Reste sous 280 caractères pour un engagement optimal

                Rédigez uniquement le post Facebook, sans commentaires.
                """;
        }

        /// <summary>
        /// Call Gemini API directly with a prompt (for AI extraction)
        /// </summary>
        public async Task<string> CallGeminiAPI(string prompt)
        {
            await _rateLimitSemaphore.WaitAsync();
            try
            {
                await EnforceRateLimit();
                var response = await _model.GenerateContentAsync(prompt);
                return response.Text() ?? "";
            }
            finally
            {
                _rateLimitSemaphore.Release();
            }
        }

        /// <summary>
        /// Generate content with retry logic and error handling
        /// </summary>
        private async Task<string> GenerateWithRetryAsync(string prompt, string context, int maxRetries = 3)
        {
            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    var response = await _model.GenerateContentAsync(prompt);
                    var result = response.Text() ?? "";

                    if (IsValidPost(result))
                        return result;

                    if (attempt == maxRetries)
                        return CreateFallbackPost(context);
                }
                catch (Exception ex) when (attempt < maxRetries)
                {
                    var isRateLimit = ex.Message.Contains("429") || ex.Message.Contains("rate limit");
                    var delay = isRateLimit ? TimeSpan.FromMinutes(1) : TimeSpan.FromSeconds(attempt * 2);
                    
                    Console.WriteLine($"⚠️ API attempt {attempt} failed: {ex.Message}");
                    Console.WriteLine($"🔄 Retrying in {delay.TotalSeconds} seconds...");
                    
                    await Task.Delay(delay);
                }
            }

            return CreateFallbackPost(context);
        }

        /// <summary>
        /// Cache management
        /// </summary>
        private string GenerateCacheKey(ProductInfo product)
        {
            return $"product_{product.ProductUrl.GetHashCode():X}_{product.Price.GetHashCode():X}";
        }

        private bool TryGetFromCache(string key, out string post)
        {
            post = "";
            
            if (!_postCache.TryGetValue(key, out post))
                return false;

            if (_cacheTimestamps.TryGetValue(key, out var timestamp) && 
                DateTime.UtcNow - timestamp > _cacheExpiry)
            {
                _postCache.TryRemove(key, out _);
                _cacheTimestamps.Remove(key);
                return false;
            }

            return true;
        }

        private void CachePost(string key, string post)
        {
            _postCache[key] = post;
            _cacheTimestamps[key] = DateTime.UtcNow;
        }

        /// <summary>
        /// Rate limiting enforcement
        /// </summary>
        private async Task EnforceRateLimit()
        {
            var timeSinceLastCall = DateTime.UtcNow - _lastApiCall;
            if (timeSinceLastCall < _minApiInterval)
            {
                var delay = _minApiInterval - timeSinceLastCall;
                await Task.Delay(delay);
            }
            _lastApiCall = DateTime.UtcNow;
        }

        /// <summary>
        /// Validation and fallback methods
        /// </summary>
        private static bool IsValidPost(string post)
        {
            return !string.IsNullOrWhiteSpace(post) && 
                   post.Length > 20 && 
                   !post.Contains("Erreur") &&
                   !post.Contains("Error");
        }

        private static string CreateFallbackPost(string context)
        {
            return $"""
                🛒 Découvrez cette offre Amazon intéressante !
                
                Un produit qui pourrait vous plaire avec un bon rapport qualité-prix.
                
                👉 Voir l'offre : {context}
                
                #Amazon #BonPlan #Shopping
                """;
        }

        private static string CreateErrorPost(ProductInfo product, string error)
        {
            return $"""
                🛒 {product.Title}
                
                Prix : {product.Price} {product.Discount}
                
                👉 Voir sur Amazon : {product.AffiliateLink}
                
                #Amazon #Deal
                """;
        }

        private static string CreateFallbackPrompt(string productUrl, string affiliateLink)
        {
            return $"""
                Créez un post Facebook pour ce produit Amazon : {productUrl}
                
                Utilisez ce lien d'affiliation : {affiliateLink}
                
                Le post doit être engageant, utiliser des emojis, et rester sous 280 caractères.
                """;
        }

        /// <summary>
        /// Dispose pattern
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _rateLimitSemaphore.Dispose();
                _disposed = true;
            }
        }
    }
}
