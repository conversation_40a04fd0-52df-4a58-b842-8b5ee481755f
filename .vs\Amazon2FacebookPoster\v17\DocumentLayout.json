{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\Amazon2FacebookPoster\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{9EC102C2-9CBF-4986-B86C-B8BE8F02CE6F}|Amazon2FacebookPoster.csproj|c:\\users\\<USER>\\source\\repos\\amazon2facebookposter\\optimizedprogram.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9EC102C2-9CBF-4986-B86C-B8BE8F02CE6F}|Amazon2FacebookPoster.csproj|solutionrelative:optimizedprogram.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9EC102C2-9CBF-4986-B86C-B8BE8F02CE6F}|Amazon2FacebookPoster.csproj|c:\\users\\<USER>\\source\\repos\\amazon2facebookposter\\extraction-rules.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{9EC102C2-9CBF-4986-B86C-B8BE8F02CE6F}|Amazon2FacebookPoster.csproj|solutionrelative:extraction-rules.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{9EC102C2-9CBF-4986-B86C-B8BE8F02CE6F}|Amazon2FacebookPoster.csproj|c:\\users\\<USER>\\source\\repos\\amazon2facebookposter\\extraction-rules-schema.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{9EC102C2-9CBF-4986-B86C-B8BE8F02CE6F}|Amazon2FacebookPoster.csproj|solutionrelative:extraction-rules-schema.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 4, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "extraction-rules.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Amazon2FacebookPoster\\extraction-rules.json", "RelativeDocumentMoniker": "extraction-rules.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Amazon2FacebookPoster\\extraction-rules.json", "RelativeToolTip": "extraction-rules.json", "ViewState": "AgIAAA0CAAAAAAAAAAAIwCACAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-22T12:22:30.543Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "extraction-rules-schema.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Amazon2FacebookPoster\\extraction-rules-schema.json", "RelativeDocumentMoniker": "extraction-rules-schema.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Amazon2FacebookPoster\\extraction-rules-schema.json", "RelativeToolTip": "extraction-rules-schema.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-22T12:22:32.185Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "OptimizedProgram.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Amazon2FacebookPoster\\OptimizedProgram.cs", "RelativeDocumentMoniker": "OptimizedProgram.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Amazon2FacebookPoster\\OptimizedProgram.cs", "RelativeToolTip": "OptimizedProgram.cs", "ViewState": "AgIAACkAAAAAAAAAAAAQwEsAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-22T12:22:20.134Z", "EditorCaption": ""}]}]}]}