{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://amazon2facebookposter.com/schemas/extraction-rules.json", "title": "Amazon Extraction Rules Configuration", "description": "Schema for configuring Amazon product extraction rules", "type": "object", "required": ["version", "regions", "extractionRules"], "properties": {"$schema": {"type": "string", "description": "JSON Schema reference"}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$", "description": "Configuration version in semver format"}, "lastUpdated": {"type": "string", "format": "date-time", "description": "Last update timestamp"}, "description": {"type": "string", "description": "Configuration description"}, "globalSettings": {"type": "object", "description": "Global settings for the scraper", "properties": {"defaultRegion": {"type": "string", "description": "Default Amazon region to use (e.g., amazon.fr)"}, "scrapingFrequency": {"type": "string", "enum": ["manual", "hourly", "daily", "weekly"], "description": "How often to run the scraper automatically"}, "proxySettings": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "server": {"type": "string", "format": "hostname"}, "port": {"type": "integer", "minimum": 1, "maximum": 65535}, "username": {"type": "string"}, "password": {"type": "string"}}}, "userAgentSettings": {"type": "object", "properties": {"strategy": {"type": "string", "enum": ["default", "random", "custom"]}, "customUserAgent": {"type": "string"}, "rotationInterval": {"type": "integer", "minimum": 0}}}}}, "regions": {"type": "object", "description": "Amazon marketplace regions configuration", "patternProperties": {"^amazon\\.[a-z]{2,3}$": {"type": "object", "required": ["name", "baseUrl", "currency", "language"], "properties": {"name": {"type": "string", "description": "Human-readable region name"}, "baseUrl": {"type": "string", "format": "uri", "description": "Base URL for the region"}, "currency": {"type": "string", "pattern": "^[A-Z]{3}$", "description": "ISO currency code"}, "language": {"type": "string", "pattern": "^[a-z]{2}$", "description": "ISO language code"}, "dealsUrl": {"type": "string", "format": "uri", "description": "URL for deals page"}}}}}, "extractionRules": {"type": "object", "description": "Extraction rules for different page types", "properties": {"productList": {"$ref": "#/definitions/extractionRuleSet"}, "productPage": {"$ref": "#/definitions/extractionRuleSet"}}}, "transformations": {"type": "object", "description": "Data transformation functions", "patternProperties": {"^[a-zA-Z][a-zA-Z0-9_]*$": {"type": "object", "required": ["description", "function"], "properties": {"description": {"type": "string"}, "function": {"type": "string", "description": "JavaScript function as string"}}}}}, "validation": {"type": "object", "description": "Global validation rules", "properties": {"globalRules": {"type": "object", "properties": {"maxExtractionTime": {"type": "integer", "minimum": 1000, "description": "Maximum extraction time in milliseconds"}, "minProductsPerPage": {"type": "integer", "minimum": 0}, "maxProductsPerPage": {"type": "integer", "minimum": 1}}}, "fieldRules": {"type": "object", "patternProperties": {"^[a-zA-Z][a-zA-Z0-9_]*$": {"$ref": "#/definitions/fieldValidation"}}}}}}, "definitions": {"extractionRuleSet": {"type": "object", "description": "Set of extraction rules for a page type", "required": ["description", "containerSelectors", "fields"], "properties": {"description": {"type": "string"}, "containerSelectors": {"type": "object", "required": ["priority", "description", "selectors"], "properties": {"priority": {"type": "integer", "minimum": 1}, "description": {"type": "string"}, "selectors": {"type": "array", "minItems": 1, "items": {"$ref": "#/definitions/selector"}}}}, "fields": {"type": "object", "patternProperties": {"^[a-zA-Z][a-zA-Z0-9_]*$": {"$ref": "#/definitions/fieldExtraction"}}}}}, "selector": {"type": "object", "required": ["selector", "priority", "description"], "properties": {"selector": {"type": "string", "minLength": 1, "description": "CSS selector string"}, "priority": {"type": "integer", "minimum": 1, "description": "Selector priority (lower = higher priority)"}, "description": {"type": "string", "description": "Human-readable description"}, "required": {"type": "array", "items": {"type": "string"}, "description": "Required attributes for this selector"}, "extraction": {"type": "string", "enum": ["textContent", "innerHTML", "href", "src", "data-*", "attribute"], "description": "How to extract data from element"}, "transform": {"type": "string", "description": "Transformation function to apply"}}}, "fieldExtraction": {"type": "object", "required": ["description", "selectors"], "properties": {"description": {"type": "string"}, "selectors": {"type": "array", "minItems": 1, "items": {"$ref": "#/definitions/selector"}}, "validation": {"$ref": "#/definitions/fieldValidation"}, "fallback": {"type": "object", "properties": {"generateFromASIN": {"type": "boolean"}, "template": {"type": "string"}, "extractFromUrl": {"type": "boolean"}, "pattern": {"type": "string"}}}}}, "fieldValidation": {"type": "object", "properties": {"required": {"type": "boolean"}, "minLength": {"type": "integer", "minimum": 0}, "maxLength": {"type": "integer", "minimum": 1}, "pattern": {"type": "string", "description": "Regular expression pattern"}, "mustContain": {"type": "array", "items": {"type": "string"}}}}}}