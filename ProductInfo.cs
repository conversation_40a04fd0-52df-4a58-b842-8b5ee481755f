using System.Text.Json.Serialization;
using System.ComponentModel.DataAnnotations;

namespace Amazon2FacebookPoster
{
    /// <summary>
    /// Optimized product information model with validation and improved serialization
    /// </summary>
    public class ProductInfo
    {
        [Required]
        [JsonPropertyName("title")]
        public string Title { get; set; } = "";

        [Required]
        [Url]
        [JsonPropertyName("productUrl")]
        public string ProductUrl { get; set; } = "";

        [Url]
        [JsonPropertyName("imageUrl")]
        public string ImageUrl { get; set; } = "";

        [JsonPropertyName("price")]
        public string Price { get; set; } = "";

        [JsonPropertyName("originalPrice")]
        public string OriginalPrice { get; set; } = "";

        [JsonPropertyName("discount")]
        public string Discount { get; set; } = "";

        [JsonPropertyName("rating")]
        public string Rating { get; set; } = "";

        [JsonPropertyName("reviewCount")]
        public string ReviewCount { get; set; } = "";

        [JsonPropertyName("category")]
        public string Category { get; set; } = "";

        [Url]
        [JsonPropertyName("affiliateLink")]
        public string AffiliateLink { get; set; } = "";

        [JsonPropertyName("commissionRate")]
        public string CommissionRate { get; set; } = "";

        [JsonPropertyName("facebookPost")]
        public string FacebookPost { get; set; } = "";

        [JsonPropertyName("isDeal")]
        public bool IsDeal { get; set; } = false;

        [JsonPropertyName("extractedAt")]
        public DateTime ExtractedAt { get; set; } = DateTime.UtcNow;

        [JsonPropertyName("asin")]
        public string ASIN => !string.IsNullOrEmpty(_asin) ? _asin : ExtractASIN(ProductUrl);

        [JsonPropertyName("dealId")]
        public string DealId { get; set; } = "";

        [JsonPropertyName("dealType")]
        public string DealType { get; set; } = "";

        // Private field to store ASIN when extracted directly from data attribute
        private string _asin = "";

        /// <summary>
        /// Validates the product information
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Title) &&
                   !string.IsNullOrWhiteSpace(ProductUrl) &&
                   Uri.TryCreate(ProductUrl, UriKind.Absolute, out _);
        }

        /// <summary>
        /// Extracts ASIN from product URL
        /// </summary>
        private static string ExtractASIN(string url)
        {
            if (string.IsNullOrEmpty(url)) return "";
            var match = System.Text.RegularExpressions.Regex.Match(url, @"/dp/([A-Z0-9]{10})");
            return match.Success ? match.Groups[1].Value : "";
        }

        /// <summary>
        /// Sets the ASIN directly (used when extracted from data attributes)
        /// </summary>
        public void SetASIN(string asin)
        {
            _asin = asin ?? "";
        }

        /// <summary>
        /// Creates a summary for logging/display purposes
        /// </summary>
        public string GetSummary()
        {
            var titlePreview = Title.Length > 50 ? Title[..47] + "..." : Title;
            var dealInfo = !string.IsNullOrEmpty(DealType) ? $" | {DealType}" : "";
            return $"{titlePreview} | {Price} | {Discount}{dealInfo}";
        }
    }
}
